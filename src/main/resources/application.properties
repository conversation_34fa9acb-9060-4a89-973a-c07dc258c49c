spring.application.name=xyhelper_fox
spring.main.allow-circular-references=true
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
mybatis-plus.configuration.map-underscore-to-camel-case=false
mybatis-plus.global-config.db-config.id-type=auto
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=0
spring.flyway.clean-disabled=true
spring.flyway.enabled=true
spring.flyway.execute-in-transaction=true
spring.flyway.validate-on-migrate=false
spring.flyway.locations=classpath:db/migration
server.port=6956
# ??????
spring.web.resources.static-locations=classpath:static/
# ???????????
spring.web.resources.add-mappings=false
spring.datasource.hikari.max-lifetime=70000

# 从环境变量读取授权码
auth.license-key=${AUTH_LICENSE_KEY:}
