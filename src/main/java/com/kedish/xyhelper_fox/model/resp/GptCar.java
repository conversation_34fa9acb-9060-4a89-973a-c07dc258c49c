package com.kedish.xyhelper_fox.model.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kedish.xyhelper_fox.repo.model.ChatGptSession;
import lombok.Data;

@Data
public class GptCar {
    //8位 随机码，不可重复，包含大小写字母数字，例如znzrFUqI
    private String carID;

    //使用次数
    private Integer count;

    //备注
    private String remark;

    // plus team pro
    private String label;

    //是否是plus
    private Integer isPlus;

    private Integer isPro;

    //推荐，空闲，可用，繁忙
    private String desc;

    @JsonIgnore
    private boolean isVirtual;

    private int clearsIn = -1;

    public boolean isTeam(){
        return isPlus != null && isPlus == 1 && "TEAM".equals(label);
    }

    public boolean isPro(){
        return isPlus != null && isPlus == 1 && "PRO".equals(label);
    }

    public static GptCar fromChatGptSession(ChatGptSession session) {
        GptCar car = new GptCar();
        car.setCarID(session.getCarID());
        car.setCount(Math.toIntExact(session.getCount()));
        car.setRemark(session.getRemark());
        car.setVirtual(false);
        if (session.getIsPlus() != null && session.getIsPlus() == 1) {
            car.setLabel("PLUS");
            car.setIsPlus(1);
        }
        return car;
    }

    public static ClaudeCar toClaudeCar(GptCar gptCar) {
        ClaudeCar claudeCar = new ClaudeCar();
        claudeCar.setCarID(gptCar.getCarID());
        claudeCar.setDesc(gptCar.getDesc());
        claudeCar.setIsPro(gptCar.getIsPro());
        claudeCar.setVirtual(gptCar.isVirtual);
        return claudeCar;
    }

    public static GrokCar toGrokCar(GptCar gptCar) {
        GrokCar grokCar = new GrokCar();
        grokCar.setCarID(gptCar.getCarID());
        grokCar.setDesc(gptCar.getDesc());
        grokCar.setIsPro(gptCar.getIsPro());
        grokCar.setRemark(gptCar.getRemark());
        grokCar.setVirtual(gptCar.isVirtual);
        return grokCar;
    }
}
